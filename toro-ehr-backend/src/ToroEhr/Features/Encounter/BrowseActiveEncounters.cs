using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.Encounter;

public record BrowseActiveEncountersQuery(PagedSearchParams PagedSearchParams, bool ShowAll)
    : AuthRequest<PaginatedList<ActiveEncounterResponse>>;

public record ActiveEncounterResponse(
    string Id,
    string PatientId,
    string PatientName,
    string PractitionerId,
    string PractitionerName,
    DateTimeOffset StartAt,
    string Status);

internal class BrowseActiveEncountersAuth : IAuth<BrowseActiveEncountersQuery, PaginatedList<ActiveEncounterResponse>>
{
    public BrowseActiveEncountersAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal class
    BrowseActiveEncountersHandler : IRequestHandler<BrowseActiveEncountersQuery, PaginatedList<ActiveEncounterResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public BrowseActiveEncountersHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<PaginatedList<ActiveEncounterResponse>> Handle(BrowseActiveEncountersQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        IRavenQueryable<Domain.Encounter> dbQuery = session.Query<Domain.Encounter>()
            .Include(x => x.PatientId)
            .Include(x => x.PractitionerId)
            .Where(x => x.LocationId == _user.SelectedLocationId && x.StartAt.Date == query.Timestamp.Date);

        if (query.ShowAll == false)
        {
            dbQuery = dbQuery.Where(x => x.PractitionerId == _user.EmployeeId);
        }

        IEnumerable<Domain.Encounter> encounters = await dbQuery
            .Statistics(out QueryStatistics stats)
            .Skip((query.PagedSearchParams.PageNumber - 1) * query.PagedSearchParams.PageSize)
            .Take(query.PagedSearchParams.PageSize)
            .ToListAsync(token: cancellationToken);

        var patientIds = encounters.Select(x => x.PatientId).ToList();
        var patients = await session.LoadAsync<Domain.Patient>(patientIds, cancellationToken);
        
        var practitionerIds = encounters.Select(x => x.PractitionerId).ToList();
        var practitioners = await session.LoadAsync<Domain.Employee>(practitionerIds, cancellationToken);

        return PaginatedList<ActiveEncounterResponse>.Create(
            encounters
                .Select(x => new ActiveEncounterResponse(
                    x.Id,
                    x.PatientId,
                    patients[x.PatientId].FullName,
                    x.PractitionerId,
                    practitioners[x.PractitionerId].FullName,
                    x.StartAt,
                    x.Status)).ToList(),
            stats.TotalResults,
            query.PagedSearchParams.PageNumber,
            query.PagedSearchParams.PageSize
        );
    }
}