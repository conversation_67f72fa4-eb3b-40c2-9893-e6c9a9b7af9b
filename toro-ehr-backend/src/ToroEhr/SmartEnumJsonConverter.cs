using System.Reflection;
using Ardalis.SmartEnum;
using Newtonsoft.Json;
using NJsonSchema;
using NJsonSchema.Generation;

namespace ToroEhr;

public class SmartEnumJsonConverter<TEnum> : JsonConverter<TEnum> where TEnum : SmartEnum<TEnum, int>
{
    public override bool CanRead => true;

    public override void WriteJ<PERSON>(JsonWriter writer, TEnum? value, JsonSerializer serializer)
    {
        if (value != null) writer.WriteValue(value.Value);
    }

    public override TEnum? ReadJson(JsonReader reader, Type objectType, TEnum? existingValue,
        bool hasExistingValue,
        JsonSerializer serializer)
    {
        var intValue = reader.Value as int? ?? int.Parse(reader.Value.ToString());
        return SmartEnum<TEnum, int>.FromValue(intValue);
    }
}

public class SmartEnumStringJsonConverter<TEnum> : JsonConverter<TEnum> where TEnum : SmartEnum<TEnum, string>
{
    public override bool CanRead => true;

    public override void WriteJson(JsonWriter writer, TEnum? value, JsonSerializer serializer)
    {
        if (value != null) writer.WriteValue(value.Value);
    }

    public override TEnum? ReadJson(JsonReader reader, Type objectType, TEnum? existingValue,
        bool hasExistingValue,
        JsonSerializer serializer)
    {
        var stringValue = reader.Value?.ToString();
        if (string.IsNullOrEmpty(stringValue)) return null;
        return SmartEnum<TEnum, string>.FromValue(stringValue);
    }
}

public class SmartEnumSchemaProcessor<TEnum> : ISchemaProcessor
    where TEnum : SmartEnum<TEnum, string>
{
    public void Process(SchemaProcessorContext context)
    {
        if (context.ContextualType.Type == typeof(TEnum))
        {
            var schema = context.Schema;
            schema.Type = JsonObjectType.String;
            schema.Enumeration.Clear();

            foreach (var e in SmartEnum<TEnum, string>.List)
            {
                schema.Enumeration.Add(e.Value); // e.Value is string, can contain spaces
            }

            schema.Description ??= typeof(TEnum).Name;
        }
    }
}

public class SmartEnumIntSchemaProcessor<TEnum> : ISchemaProcessor
    where TEnum : SmartEnum<TEnum, int>
{
    public void Process(SchemaProcessorContext context)
    {
        if (context.ContextualType.Type == typeof(TEnum))
        {
            var schema = context.Schema;
            schema.Type = JsonObjectType.Integer;
            schema.Enumeration.Clear();

            foreach (var e in SmartEnum<TEnum, int>.List)
            {
                schema.Enumeration.Add(e.Value);
            }

            schema.Description ??= typeof(TEnum).Name;
        }
    }
}

public static class SmartEnumSchemaExtensions
{
    /// <summary>
    /// automatically registers all smart enums found in the specified assemblies
    /// </summary>
    public static void AddSmartEnumSchemaProcessors(this NJsonSchema.Generation.JsonSchemaGeneratorSettings settings, params Assembly[] assemblies)
    {
        var smartEnumTypes = new List<Type>();

        foreach (var assembly in assemblies)
        {
            smartEnumTypes.AddRange(GetSmartEnumTypes(assembly));
        }

        foreach (var enumType in smartEnumTypes)
        {
            var processorType = CreateSchemaProcessorType(enumType);
            if (processorType != null)
            {
                var processor = Activator.CreateInstance(processorType);
                if (processor is ISchemaProcessor schemaProcessor)
                {
                    settings.SchemaProcessors.Add(schemaProcessor);
                }
            }
        }
    }

    private static IEnumerable<Type> GetSmartEnumTypes(Assembly assembly)
    {
        return assembly.GetTypes()
            .Where(type => type.IsClass && !type.IsAbstract && IsSmartEnum(type));
    }

    private static bool IsSmartEnum(Type type)
    {
        var baseType = type.BaseType;
        while (baseType != null)
        {
            if (baseType.IsGenericType)
            {
                var genericType = baseType.GetGenericTypeDefinition();
                if (genericType == typeof(SmartEnum<,>))
                {
                    return true;
                }
            }
            baseType = baseType.BaseType;
        }
        return false;
    }

    private static Type? CreateSchemaProcessorType(Type smartEnumType)
    {
        var baseType = GetSmartEnumBaseType(smartEnumType);
        if (baseType == null) return null;

        var valueType = baseType.GetGenericArguments()[1];

        if (valueType == typeof(string))
        {
            return typeof(SmartEnumSchemaProcessor<>).MakeGenericType(smartEnumType);
        }
        else if (valueType == typeof(int))
        {
            return typeof(SmartEnumIntSchemaProcessor<>).MakeGenericType(smartEnumType);
        }

        return null;
    }

    private static Type? GetSmartEnumBaseType(Type type)
    {
        var baseType = type.BaseType;
        while (baseType != null)
        {
            if (baseType.IsGenericType && baseType.GetGenericTypeDefinition() == typeof(SmartEnum<,>))
            {
                return baseType;
            }
            baseType = baseType.BaseType;
        }
        return null;
    }
}